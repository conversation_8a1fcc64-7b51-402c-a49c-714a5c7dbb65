import numpy as np
import cv2

class MedicalAnalyzer:
    def __init__(self):
        self.normal_ranges = {
            'knee_angle': (160, 180),
            'bone_density': (0.3, 0.8),
            'bone_length_ratio': (0.8, 1.2)
        }
    
    def analyze(self, image, detections, masks, keypoints):
        analysis = {
            'bone_count': len(detections),
            'detected_bones': [det['class'] for det in detections],
            'measurements': self.calculate_measurements(masks, keypoints),
            'abnormalities': self.detect_abnormalities(detections, keypoints),
            'recommendations': []
        }
        
        # Generate recommendations
        analysis['recommendations'] = self.generate_recommendations(analysis)
        
        return analysis
    
    def calculate_measurements(self, masks, keypoints):
        measurements = {}
        
        # Bone area measurements
        for i, bone_name in enumerate(['femur', 'tibia', 'fibula', 'ankle']):
            if i < len(masks):
                area = np.sum(masks[i])
                measurements[f'{bone_name}_area'] = int(area)
        
        # Joint angles
        if keypoints:
            hip = next((kp for kp in keypoints if kp['name'] == 'hip_joint'), None)
            knee = next((kp for kp in keypoints if kp['name'] == 'knee_joint'), None)
            ankle = next((kp for kp in keypoints if kp['name'] == 'ankle_joint'), None)
            
            if hip and knee and ankle:
                measurements['leg_length'] = self.calculate_distance(
                    (hip['x'], hip['y']), (ankle['x'], ankle['y'])
                )
        
        return measurements
    
    def detect_abnormalities(self, detections, keypoints):
        abnormalities = []
        
        # Check for missing bones
        expected_bones = ['femur', 'tibia', 'fibula']
        detected_bones = [det['class'] for det in detections]
        
        for bone in expected_bones:
            if bone not in detected_bones:
                abnormalities.append(f"Missing {bone} detection")
        
        # Check joint angles
        if keypoints:
            angles = self.calculate_joint_angles(keypoints)
            for angle_name, angle_value in angles.items():
                normal_range = self.normal_ranges.get(angle_name)
                if normal_range and not (normal_range[0] <= angle_value <= normal_range[1]):
                    abnormalities.append(f"Abnormal {angle_name}: {angle_value:.1f}°")
        
        return abnormalities
    
    def calculate_joint_angles(self, keypoints):
        angles = {}
        
        hip = next((kp for kp in keypoints if kp['name'] == 'hip_joint'), None)
        knee = next((kp for kp in keypoints if kp['name'] == 'knee_joint'), None)
        ankle = next((kp for kp in keypoints if kp['name'] == 'ankle_joint'), None)
        
        if hip and knee and ankle:
            angles['knee_angle'] = self.calculate_angle(
                (hip['x'], hip['y']),
                (knee['x'], knee['y']),
                (ankle['x'], ankle['y'])
            )
        
        return angles
    
    def calculate_distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def calculate_angle(self, p1, p2, p3):
        v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
        v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0))
        
        return np.degrees(angle)
    
    def generate_recommendations(self, analysis):
        recommendations = []
        
        if analysis['bone_count'] < 3:
            recommendations.append("Consider retaking X-ray with better positioning")
        
        if analysis['abnormalities']:
            recommendations.append("Consult with radiologist for detailed examination")
        
        if 'knee_angle' in analysis.get('measurements', {}):
            angle = analysis['measurements']['knee_angle']
            if angle < 160:
                recommendations.append("Possible knee flexion contracture")
        
        return recommendations