from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import cv2
import numpy as np
import io
import base64
from PIL import Image
from models.yolo_detector import YOLODetector
from models.segmentation_model import SegmentationModel
from models.keypoint_detector import KeypointDetector
from utils.medical_analysis import MedicalAnalyzer

app = Flask(__name__)
CORS(app)

# Initialize models
yolo_detector = YOLODetector('models/yolo_weights.pt')
segmentation_model = SegmentationModel('models/segmentation_weights.h5')
keypoint_detector = KeypointDetector('models/keypoint_weights.h5')
medical_analyzer = MedicalAnalyzer()

@app.route('/api/detect', methods=['POST'])
def detect_bones():
    try:
        # Get image from request
        image_data = request.json['image']
        image_bytes = base64.b64decode(image_data.split(',')[1])
        image = Image.open(io.BytesIO(image_bytes))
        image_np = np.array(image)
        
        # Run detection
        detections = yolo_detector.detect(image_np)
        
        return jsonify({
            'success': True,
            'detections': detections,
            'count': len(detections)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/segment', methods=['POST'])
def segment_bones():
    try:
        image_data = request.json['image']
        image_bytes = base64.b64decode(image_data.split(',')[1])
        image = Image.open(io.BytesIO(image_bytes))
        image_np = np.array(image)
        
        # Run segmentation
        masks = segmentation_model.segment(image_np)
        
        # Convert masks to base64
        mask_images = []
        for mask in masks:
            mask_pil = Image.fromarray((mask * 255).astype(np.uint8))
            buffer = io.BytesIO()
            mask_pil.save(buffer, format='PNG')
            mask_b64 = base64.b64encode(buffer.getvalue()).decode()
            mask_images.append(f"data:image/png;base64,{mask_b64}")
        
        return jsonify({
            'success': True,
            'masks': mask_images,
            'bone_areas': [np.sum(mask) for mask in masks]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/keypoints', methods=['POST'])
def detect_keypoints():
    try:
        image_data = request.json['image']
        image_bytes = base64.b64decode(image_data.split(',')[1])
        image = Image.open(io.BytesIO(image_bytes))
        image_np = np.array(image)
        
        # Detect keypoints
        keypoints = keypoint_detector.detect(image_np)
        
        return jsonify({
            'success': True,
            'keypoints': keypoints,
            'joint_angles': keypoint_detector.calculate_angles(keypoints)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/analyze', methods=['POST'])
def medical_analysis():
    try:
        image_data = request.json['image']
        image_bytes = base64.b64decode(image_data.split(',')[1])
        image = Image.open(io.BytesIO(image_bytes))
        image_np = np.array(image)
        
        # Comprehensive analysis
        detections = yolo_detector.detect(image_np)
        masks = segmentation_model.segment(image_np)
        keypoints = keypoint_detector.detect(image_np)
        
        # Medical analysis
        analysis = medical_analyzer.analyze(image_np, detections, masks, keypoints)
        
        return jsonify({
            'success': True,
            'analysis': analysis
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)