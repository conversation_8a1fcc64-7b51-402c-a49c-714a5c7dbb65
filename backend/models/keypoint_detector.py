import tensorflow as tf
import numpy as np
import cv2
import math

class KeypointDetector:
    def __init__(self, model_path):
        self.model = tf.keras.models.load_model(model_path)
        self.keypoint_names = [
            'hip_joint', 'knee_joint', 'ankle_joint',
            'femur_head', 'femur_neck', 'tibia_plateau',
            'fibula_head', 'malleolus_lateral', 'malleolus_medial'
        ]
        
    def detect(self, image):
        # Preprocess
        image_resized = cv2.resize(image, (256, 256))
        image_normalized = image_resized.astype(np.float32) / 255.0
        input_image = np.expand_dims(image_normalized, axis=0)
        
        # Predict keypoints
        predictions = self.model.predict(input_image)
        
        # Convert to original image coordinates
        h, w = image.shape[:2]
        keypoints = []
        
        for i in range(0, len(predictions[0]), 2):
            x = predictions[0][i] * w
            y = predictions[0][i+1] * h
            keypoints.append({
                'name': self.keypoint_names[i//2],
                'x': float(x),
                'y': float(y),
                'confidence': float(predictions[0][i+2]) if i+2 < len(predictions[0]) else 1.0
            })
        
        return keypoints
    
    def calculate_angles(self, keypoints):
        angles = {}
        
        # Find specific keypoints
        hip = next((kp for kp in keypoints if kp['name'] == 'hip_joint'), None)
        knee = next((kp for kp in keypoints if kp['name'] == 'knee_joint'), None)
        ankle = next((kp for kp in keypoints if kp['name'] == 'ankle_joint'), None)
        
        if hip and knee and ankle:
            # Calculate knee angle
            angle = self.calculate_angle(
                (hip['x'], hip['y']),
                (knee['x'], knee['y']),
                (ankle['x'], ankle['y'])
            )
            angles['knee_angle'] = angle
        
        return angles
    
    def calculate_angle(self, p1, p2, p3):
        # Calculate angle between three points
        v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
        v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0))
        
        return math.degrees(angle)