leg-xray-detection/
├── backend/
│   ├── app.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── yolo_detector.py
│   │   ├── segmentation_model.py
│   │   └── keypoint_detector.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── image_processing.py
│   │   └── medical_analysis.py
│   └── requirements.txt
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── App.js
│   └── package.json
├── ml_training/
│   ├── train_yolo.py
│   ├── train_segmentation.py
│   └── train_keypoints.py
└── docker-compose.yml