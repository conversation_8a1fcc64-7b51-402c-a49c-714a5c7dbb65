import React, { useState } from 'react';
import ImageUpload from './components/ImageUpload';
import DetectionResults from './components/DetectionResults';
import SegmentationView from './components/SegmentationView';
import KeypointView from './components/KeypointView';
import MedicalAnalysis from './components/MedicalAnalysis';
import './App.css';

function App() {
  const [uploadedImage, setUploadedImage] = useState(null);
  const [activeTab, setActiveTab] = useState('detection');
  const [results, setResults] = useState({});

  const handleImageUpload = (imageData) => {
    setUploadedImage(imageData);
    setResults({});
  };

  const tabs = [
    { id: 'detection', label: 'Object Detection', component: DetectionResults },
    { id: 'segmentation', label: 'Segmentation', component: SegmentationView },
    { id: 'keypoints', label: 'Keypoint Detection', component: KeypointView },
    { id: 'analysis', label: 'Medical Analysis', component: MedicalAnalysis }
  ];

  return (
    <div className="App">
      <header className="app-header">
        <h1>Leg X-Ray Bone Detection System</h1>
        <p>Advanced Computer Vision for Medical Imaging</p>
      </header>

      <main className="app-main">
        <div className="upload-section">
          <ImageUpload onImageUpload={handleImageUpload} />
        </div>

        {uploadedImage && (
          <div className="analysis-section">
            <div className="tab-navigation">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            <div className="tab-content">
              {tabs.map(tab => {
                const Component = tab.component;
                return (
                  <div
                    key={tab.id}
                    className={`tab-panel ${activeTab === tab.id ? 'active' : ''}`}
                  >
                    {activeTab === tab.id && (
                      <Component
                        image={uploadedImage}
                        results={results[tab.id]}
                        onResults={(data) => setResults(prev => ({ ...prev, [tab.id]: data }))}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;