.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  text-align: center;
  padding: 2rem;
  color: white;
}

.app-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.upload-section {
  margin-bottom: 2rem;
}

.dropzone {
  border: 2px dashed #ffffff50;
  border-radius: 10px;
  padding: 3rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
}

.dropzone:hover,
.dropzone.active {
  border-color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
}

.upload-content {
  color: white;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.upload-button {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 1rem;
  font-size: 1rem;
}

.tab-navigation {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}

.tab-button {
  flex: 1;
  padding: 1rem;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  transition: background 0.3s ease;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.2);
  font-weight: bold;
}

.tab-content {
  background: white;
  border-radius: 0 0 10px 10px;
  min-height: 500px;
}

.tab-panel {
  display: none;
  padding: 2rem;
}

.tab-panel.active {
  display: block;
}

.detection-results {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.image-container {
  position: relative;
  background: #f5f5f5;
  border-radius: 10px;
  overflow: hidden;
}

.xray-image {
  width: 100%;
  height: auto;
  display: block;
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.results-panel {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 10px;
}

.stats {
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-weight: bold;
}

.detection-item {
  background: white;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.bone-name {
  font-weight: bold;
  text-transform: capitalize;
}

.confidence {
  background: #4CAF50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
}

.loading-container {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 2rem;
  color: #e74c3c;
}

@media (max-width: 768px) {
  .detection-results {
    grid-template-columns: 1fr;
  }
  
  .tab-navigation {
    flex-direction: column;
  }
}