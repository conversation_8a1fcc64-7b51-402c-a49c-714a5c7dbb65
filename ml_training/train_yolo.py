
from ultralytics import YOLO
import torch
import csv
import os
from collections import defaultdict

def convert_csv_to_yolo():
    """Convert CSV annotations to YOLO format"""
    # Class mapping
    class_mapping = {'femur': 0, 'tibia': 1, 'fibula': 2, 'ankle': 3}
    
    # Process each split
    for split in ['train', 'valid', 'test']:
        csv_path = f'Dataset/{split}/_annotations.csv'
        if not os.path.exists(csv_path):
            print(f"Warning: {csv_path} not found, skipping...")
            continue
            
        # Create labels directory
        labels_dir = f'Dataset/{split}/labels'
        os.makedirs(labels_dir, exist_ok=True)

        # Read CSV and group by filename
        annotations_by_file = defaultdict(list)

        with open(csv_path, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                annotations_by_file[row['filename']].append(row)

        # Convert annotations for each file
        for filename, annotations in annotations_by_file.items():
            # Create corresponding label file
            label_filename = filename.replace('.jpg', '.txt').replace('.jpeg', '.txt')
            label_path = os.path.join(labels_dir, label_filename)

            with open(label_path, 'w') as f:
                for annotation in annotations:
                    # Convert to YOLO format (normalized coordinates)
                    width = float(annotation['width'])
                    height = float(annotation['height'])
                    xmin = float(annotation['xmin'])
                    ymin = float(annotation['ymin'])
                    xmax = float(annotation['xmax'])
                    ymax = float(annotation['ymax'])

                    # Calculate center coordinates and dimensions
                    x_center = (xmin + xmax) / 2 / width
                    y_center = (ymin + ymax) / 2 / height
                    bbox_width = (xmax - xmin) / width
                    bbox_height = (ymax - ymin) / height

                    # Get class ID
                    class_id = class_mapping[annotation['class']]

                    # Write YOLO format: class_id x_center y_center width height
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {bbox_width:.6f} {bbox_height:.6f}\n")

        print(f"Converted annotations for {split} split")

def train_yolo_model():
    # Initialize YOLO model
    model = YOLO('yolov8n.pt')
    
    # Auto-detect device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Train the model
    results = model.train(
        data='Dataset/data.yaml',  # Use relative path
        epochs=100,
        imgsz=640,
        batch=16,
        name='leg_xray_detection',
        save=True,
        plots=True,
        val=True,
        patience=10,
        device=device
    )
    
    # Validate the model
    metrics = model.val()
    
    # Export the model
    model.export(format='onnx')
    
    return results, metrics

if __name__ == "__main__":
    # Convert CSV annotations to YOLO format
    print("Converting CSV annotations to YOLO format...")
    convert_csv_to_yolo()

    # Create data.yaml file for YOLO training
    data_yaml = """path: ./Dataset
train: train
val: valid
test: test

nc: 4
names: ['femur', 'tibia', 'fibula', 'ankle']
"""

    with open('Dataset/data.yaml', 'w') as f:
        f.write(data_yaml)

    print("Starting YOLO training...")
    # Start training
    results, metrics = train_yolo_model()
    print("Training completed!")
    print(f"mAP50: {metrics.box.map50}")
    print(f"mAP50-95: {metrics.box.map}")
