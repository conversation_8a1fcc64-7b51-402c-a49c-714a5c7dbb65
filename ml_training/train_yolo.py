from ultralytics import YOLO
import os
import torch

def train_yolo_model():
    # Initialize YOLO model
    model = YOLO('yolov8n.pt')  # Start with pretrained weights
    
    # Auto-detect device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Train the model
    results = model.train(
        data='Dataset/data.yaml',  # Path to dataset config
        epochs=100,
        imgsz=640,
        batch=16,
        name='leg_xray_detection',
        save=True,
        plots=True,
        val=True,
        patience=10,
        device=device  # Use auto-detected device
    )
    
    # Validate the model
    metrics = model.val()
    
    # Export the model
    model.export(format='onnx')
    
    return results, metrics

if __name__ == "__main__":
    # Create data.yaml file for YOLO training
    data_yaml = """
path: .
train: Dataset/train/images
val: Dataset/valid/images
test: Dataset/test/images

nc: 4
names: ['femur', 'tibia', 'fibula', 'ankle']
"""
    
    with open('Dataset/data.yaml', 'w') as f:
        f.write(data_yaml)
    
    # Start training
    results, metrics = train_yolo_model()
    print("Training completed!")
    print(f"mAP50: {metrics.box.map50}")
    print(f"mAP50-95: {metrics.box.map}")
